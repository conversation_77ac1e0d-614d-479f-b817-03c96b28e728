import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from ...controllers.admin_controller import AdminController
from ...utils.validators import Validators
from ...database.db_config import get_db_connection, close_connection
from ...utils.theme import ULTheme

class EditSemesterForm:
    def __init__(self, parent, semester_id):
        self.parent = parent
        self.semester_id = semester_id
        
        # Create main frame
        self.main_frame = ttk.Frame(parent, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create form title
        title_label = ttk.Label(self.main_frame, text="Edit Semester", 
                               font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 20))
        
        # Load semester data
        self.load_semester_data()
        
        # Create form fields
        self.create_form_fields()
        
        # Create buttons
        self.create_buttons()
        
        # Status label
        self.status_label = ttk.Label(self.main_frame, text="", foreground="red")
        self.status_label.grid(row=6, column=0, columnspan=3, sticky="w", pady=5)
    
    def load_semester_data(self):
        """Load semester data from database"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM semesters WHERE id = ?", (self.semester_id,))
            self.semester_data = cursor.fetchone()
            
            close_connection(conn)
            
            if not self.semester_data:
                messagebox.showerror("Error", "Semester not found")
                return
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load semester data: {e}")
    
    def create_form_fields(self):
        """Create form input fields"""
        # Semester Name
        ttk.Label(self.main_frame, text="Semester Name:").grid(row=1, column=0, sticky="w", pady=5)
        self.name_var = tk.StringVar()
        self.name_var.set(self.semester_data['name'] if self.semester_data else "")
        self.name_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.name_var)
        self.name_entry.grid(row=1, column=1, sticky="w", pady=5)
        self.name_entry.focus()
        
        # Start Date
        ttk.Label(self.main_frame, text="Start Date (YYYY-MM-DD):").grid(row=2, column=0, sticky="w", pady=5)
        self.start_date_var = tk.StringVar()
        self.start_date_var.set(self.semester_data['start_date'] if self.semester_data else "")
        self.start_date_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.start_date_var)
        self.start_date_entry.grid(row=2, column=1, sticky="w", pady=5)
        
        # End Date
        ttk.Label(self.main_frame, text="End Date (YYYY-MM-DD):").grid(row=3, column=0, sticky="w", pady=5)
        self.end_date_var = tk.StringVar()
        self.end_date_var.set(self.semester_data['end_date'] if self.semester_data else "")
        self.end_date_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.end_date_var)
        self.end_date_entry.grid(row=3, column=1, sticky="w", pady=5)
        
        # Is Active
        ttk.Label(self.main_frame, text="Set as Active:").grid(row=4, column=0, sticky="w", pady=5)
        self.is_active_var = tk.BooleanVar()
        self.is_active_var.set(bool(self.semester_data['is_active']) if self.semester_data else False)
        self.is_active_check = ttk.Checkbutton(self.main_frame, variable=self.is_active_var)
        self.is_active_check.grid(row=4, column=1, sticky="w", pady=5)
        
        # Active status warning
        self.active_warning_label = ttk.Label(self.main_frame, 
                                            text="⚠️ Setting this as active will deactivate all other semesters", 
                                            font=("Arial", 8), foreground="orange")
        self.active_warning_label.grid(row=4, column=2, sticky="w", padx=(10, 0), pady=5)
        
        # Bind checkbox to show/hide warning
        self.is_active_var.trace('w', self.toggle_active_warning)
        self.toggle_active_warning()  # Initial state
    
    def toggle_active_warning(self, *args):
        """Show/hide active warning based on checkbox state"""
        if self.is_active_var.get():
            self.active_warning_label.grid()
        else:
            self.active_warning_label.grid_remove()
    
    def create_buttons(self):
        """Create form buttons"""
        buttons_frame = ttk.Frame(self.main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        # Update button
        update_button = ttk.Button(buttons_frame, text="Update", command=self.update_semester)
        update_button.pack(side=tk.LEFT, padx=5)
        
        # Cancel button
        cancel_button = ttk.Button(buttons_frame, text="Cancel", command=self.cancel)
        cancel_button.pack(side=tk.LEFT, padx=5)
    
    def validate_form(self):
        """Validate form data"""
        # Clear previous status
        self.status_label.config(text="")
        
        # Validate semester name
        name = self.name_var.get().strip()
        if not name:
            self.status_label.config(text="Semester name is required")
            return False
        
        # Validate start date
        start_date = self.start_date_var.get().strip()
        if not Validators.validate_date(start_date):
            self.status_label.config(text="Invalid start date format. Use YYYY-MM-DD")
            return False
        
        # Validate end date
        end_date = self.end_date_var.get().strip()
        if not Validators.validate_date(end_date):
            self.status_label.config(text="Invalid end date format. Use YYYY-MM-DD")
            return False
        
        # Validate date range
        try:
            start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            
            if end_date_obj <= start_date_obj:
                self.status_label.config(text="End date must be after start date")
                return False
                
        except ValueError:
            self.status_label.config(text="Invalid date format. Use YYYY-MM-DD")
            return False
        
        # Check for duplicate semester name (excluding current semester)
        if self.check_duplicate_name(name):
            self.status_label.config(text="Semester name already exists")
            return False
        
        return True
    
    def check_duplicate_name(self, name):
        """Check if semester name already exists (excluding current semester)"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT id FROM semesters WHERE name = ? AND id != ?", (name, self.semester_id))
            result = cursor.fetchone()
            
            close_connection(conn)
            
            return result is not None
            
        except Exception as e:
            print(f"Error checking duplicate name: {e}")
            return False
    
    def update_semester(self):
        """Update semester data"""
        if not self.validate_form():
            return
        
        # Get form data
        name = self.name_var.get().strip()
        start_date = self.start_date_var.get().strip()
        end_date = self.end_date_var.get().strip()
        is_active = self.is_active_var.get()
        
        # Update semester
        success, message = AdminController.update_semester(
            self.semester_id, name, start_date, end_date, is_active
        )
        
        if success:
            messagebox.showinfo("Success", "Semester updated successfully")
            # Refresh semesters list
            AdminController.show_semesters()
        else:
            self.status_label.config(text=message)
    
    def cancel(self):
        """Cancel editing and go back to semesters list"""
        AdminController.show_semesters()
