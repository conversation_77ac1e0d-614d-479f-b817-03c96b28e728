import sqlite3

from ..models.user import User
from ..models.student import Student
from ..models.professor import Professor
from ..models.admin import Admin
from ..models.course import Course
from ..database.db_config import get_db_connection, close_connection

class AdminController:
    @staticmethod
    def create_student(student_id, first_name, last_name, email, phone, department_id,
                      semester, enrollment_date, username, password):
        """
        Create a new student with user account
        """
        try:
            # Check if username already exists
            if User.get_by_username(username):
                return False, "Username already exists"

            # Check if student ID already exists
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM students WHERE student_id = ?", (student_id,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Student ID {student_id} already exists"

            # Check if email already exists
            cursor.execute("SELECT * FROM students WHERE email = ?", (email,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Email address {email} is already in use by another student"

            close_connection(conn)

            # Create user account
            user = User(username=username, password=password, role='student')
            user.save()

            # Create student profile
            student = Student(
                user_id=user.id,
                student_id=student_id,
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=phone,
                department_id=department_id,
                semester=semester,
                enrollment_date=enrollment_date
            )
            student.save()

            return True, "Student created successfully"

        except sqlite3.IntegrityError as e:
            # Handle specific database constraint errors
            error_message = str(e)
            if "students.email" in error_message:
                return False, f"Email address {email} is already in use by another student"
            elif "students.student_id" in error_message:
                return False, f"Student ID {student_id} already exists"
            else:
                return False, f"Database constraint error: {error_message}"
        except Exception as e:
            # Handle any other errors
            print(f"Error creating student: {e}")
            return False, f"Error creating student: {str(e)}"

    @staticmethod
    def create_professor(professor_id, first_name, last_name, email, phone, department_id,
                        specialization, joining_date, username, password):
        """
        Create a new professor with user account
        """
        try:
            # Check if username already exists
            if User.get_by_username(username):
                return False, "Username already exists"

            # Check if professor ID already exists
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM professors WHERE professor_id = ?", (professor_id,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Professor ID {professor_id} already exists"

            # Check if email already exists
            cursor.execute("SELECT * FROM professors WHERE email = ?", (email,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Email address {email} is already in use by another professor"

            close_connection(conn)

            # Create user account
            user = User(username=username, password=password, role='professor')
            user.save()

            # Create professor profile
            professor = Professor(
                user_id=user.id,
                professor_id=professor_id,
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=phone,
                department_id=department_id,
                specialization=specialization,
                joining_date=joining_date
            )
            professor.save()

            return True, "Professor created successfully"

        except sqlite3.IntegrityError as e:
            # Handle specific database constraint errors
            error_message = str(e)
            if "professors.email" in error_message:
                return False, f"Email address {email} is already in use by another professor"
            elif "professors.professor_id" in error_message:
                return False, f"Professor ID {professor_id} already exists"
            else:
                return False, f"Database constraint error: {error_message}"
        except Exception as e:
            # Handle any other errors
            print(f"Error creating professor: {e}")
            return False, f"Error creating professor: {str(e)}"

    @staticmethod
    def create_admin(admin_id, first_name, last_name, email, phone, username, password):
        """
        Create a new admin with user account
        """
        try:
            # Check if username already exists
            if User.get_by_username(username):
                return False, "Username already exists"

            # Check if admin ID already exists
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM admins WHERE admin_id = ?", (admin_id,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Admin ID {admin_id} already exists"

            # Check if email already exists
            cursor.execute("SELECT * FROM admins WHERE email = ?", (email,))
            if cursor.fetchone():
                close_connection(conn)
                return False, f"Email address {email} is already in use by another admin"

            close_connection(conn)

            # Create user account
            user = User(username=username, password=password, role='admin')
            user.save()

            # Create admin profile
            admin = Admin(
                user_id=user.id,
                admin_id=admin_id,
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=phone
            )
            admin.save()

            return True, "Admin created successfully"

        except sqlite3.IntegrityError as e:
            # Handle specific database constraint errors
            error_message = str(e)
            if "admins.email" in error_message:
                return False, f"Email address {email} is already in use by another admin"
            elif "admins.admin_id" in error_message:
                return False, f"Admin ID {admin_id} already exists"
            else:
                return False, f"Database constraint error: {error_message}"
        except Exception as e:
            # Handle any other errors
            print(f"Error creating admin: {e}")
            return False, f"Error creating admin: {str(e)}"

    @staticmethod
    def create_department(name, code):
        """
        Create a new department
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if department code already exists
        cursor.execute("SELECT * FROM departments WHERE code = ?", (code,))
        if cursor.fetchone():
            close_connection(conn)
            return False, "Department code already exists"

        # Create department
        cursor.execute(
            "INSERT INTO departments (name, code) VALUES (?, ?)",
            (name, code)
        )

        conn.commit()
        close_connection(conn)

        return True, "Department created successfully"

    @staticmethod
    def create_course(course_code, title, credit_hours, department_id, semester, description):
        """
        Create a new course
        """
        # Check if course code already exists
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM courses WHERE course_code = ?", (course_code,))
        if cursor.fetchone():
            close_connection(conn)
            return False, "Course code already exists"

        # Create course
        course = Course(
            course_code=course_code,
            title=title,
            credit_hours=credit_hours,
            department_id=department_id,
            semester=semester,
            description=description
        )
        course.save()

        return True, "Course created successfully"

    @staticmethod
    def create_semester(name, start_date, end_date, is_active=False):
        """
        Create a new semester
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        # If setting this semester as active, deactivate all other semesters
        if is_active:
            cursor.execute("UPDATE semesters SET is_active = 0")

        # Create semester
        cursor.execute(
            "INSERT INTO semesters (name, start_date, end_date, is_active) VALUES (?, ?, ?, ?)",
            (name, start_date, end_date, 1 if is_active else 0)
        )

        conn.commit()
        close_connection(conn)

        return True, "Semester created successfully"

    @staticmethod
    def assign_professor_to_course(professor_id, course_id, semester_id):
        """
        Assign a professor to a course for a specific semester
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if assignment already exists
        cursor.execute(
            "SELECT * FROM professor_assignments WHERE professor_id = ? AND course_id = ? AND semester_id = ?",
            (professor_id, course_id, semester_id)
        )
        if cursor.fetchone():
            close_connection(conn)
            return False, "Professor is already assigned to this course for this semester"

        # Create assignment
        cursor.execute(
            "INSERT INTO professor_assignments (professor_id, course_id, semester_id) VALUES (?, ?, ?)",
            (professor_id, course_id, semester_id)
        )

        conn.commit()
        close_connection(conn)

        return True, "Professor assigned to course successfully"

    @staticmethod
    def enroll_student_in_course(student_id, course_id, semester_id):
        """
        Enroll a student in a course for a specific semester
        """
        import datetime

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if enrollment already exists
        cursor.execute(
            "SELECT * FROM enrollments WHERE student_id = ? AND course_id = ? AND semester_id = ?",
            (student_id, course_id, semester_id)
        )
        if cursor.fetchone():
            close_connection(conn)
            return False, "Student is already enrolled in this course for this semester"

        # Create enrollment with current date
        cursor.execute(
            "INSERT INTO enrollments (student_id, course_id, semester_id, enrollment_date) VALUES (?, ?, ?, ?)",
            (student_id, course_id, semester_id, datetime.datetime.now().strftime('%Y-%m-%d'))
        )

        conn.commit()
        close_connection(conn)

        return True, "Student enrolled in course successfully"

    @staticmethod
    def get_all_departments():
        """
        Get all departments
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM departments ORDER BY name")
        departments = cursor.fetchall()

        close_connection(conn)

        return departments

    @staticmethod
    def get_all_semesters():
        """
        Get all semesters
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM semesters ORDER BY start_date DESC")
        semesters = cursor.fetchall()

        close_connection(conn)

        return semesters

    @staticmethod
    def get_active_semester():
        """
        Get the currently active semester
        """
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM semesters WHERE is_active = 1")
        semester = cursor.fetchone()

        close_connection(conn)

        return semester

    @staticmethod
    def update_semester(semester_id, name, start_date, end_date, is_active):
        """
        Update an existing semester
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # If setting this semester as active, deactivate all other semesters
            if is_active:
                cursor.execute("UPDATE semesters SET is_active = 0")

            # Update semester
            cursor.execute(
                "UPDATE semesters SET name = ?, start_date = ?, end_date = ?, is_active = ? WHERE id = ?",
                (name, start_date, end_date, 1 if is_active else 0, semester_id)
            )

            conn.commit()
            close_connection(conn)

            return True, "Semester updated successfully"

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"SQLite error updating semester: {e}")
            return False, f"Database error: {e}"
        except Exception as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"Error updating semester: {e}")
            return False, f"Error updating semester: {e}"

    @staticmethod
    def set_active_semester(semester_id):
        """
        Set a semester as active and deactivate all others
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Deactivate all semesters
            cursor.execute("UPDATE semesters SET is_active = 0")

            # Activate the specified semester
            cursor.execute("UPDATE semesters SET is_active = 1 WHERE id = ?", (semester_id,))

            conn.commit()
            close_connection(conn)

            return True, "Active semester updated successfully"

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"SQLite error setting active semester: {e}")
            return False, f"Database error: {e}"
        except Exception as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"Error setting active semester: {e}")
            return False, f"Error setting active semester: {e}"

    # Static variable to store reference to admin dashboard
    admin_dashboard = None

    @classmethod
    def set_admin_dashboard(cls, dashboard):
        """
        Set the admin dashboard reference
        """
        cls.admin_dashboard = dashboard

    @classmethod
    def show_add_professor_form(cls):
        """
        Show the add professor form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_add_professor()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_add_department_form(cls):
        """
        Show the add department form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_add_department()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_add_course_form(cls):
        """
        Show the add course form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_add_course()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_add_student_form(cls):
        """
        Show the add student form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_add_student()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_add_semester_form(cls):
        """
        Show the add semester form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_add_semester()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_professors(cls):
        """
        Show the professors list in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_professors()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_edit_professor_form(cls, professor_id):
        """
        Show the edit professor form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_edit_professor(professor_id)
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_assign_professor_form(cls, professor_id=None):
        """
        Show the assign professor form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_assign_professor(professor_id)
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_enroll_student_form(cls, student_id=None):
        """
        Show the enroll student form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_enroll_student(student_id)
        else:
            print("Admin dashboard reference not set")

    @staticmethod
    def update_professor(professor_id, professor_code, first_name, last_name, email, phone, department_id, specialization):
        """
        Update professor information
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if professor ID already exists for another professor
            cursor.execute(
                "SELECT id FROM professors WHERE professor_id = ? AND id != ?",
                (professor_code, professor_id)
            )
            existing_professor = cursor.fetchone()

            if existing_professor:
                close_connection(conn)
                return False, f"Professor ID {professor_code} is already in use"

            # Check if email already exists for another professor
            cursor.execute(
                "SELECT id FROM professors WHERE email = ? AND id != ?",
                (email, professor_id)
            )
            existing_email = cursor.fetchone()

            if existing_email:
                close_connection(conn)
                return False, f"Email {email} is already in use"

            # Update professor
            cursor.execute("""
                UPDATE professors
                SET professor_id = ?, first_name = ?, last_name = ?, email = ?,
                    phone = ?, department_id = ?, specialization = ?
                WHERE id = ?
            """, (
                professor_code, first_name, last_name, email,
                phone, department_id, specialization, professor_id
            ))

            conn.commit()
            close_connection(conn)

            return True, "Professor updated successfully"

        except Exception as e:
            print(f"Error updating professor: {e}")
            return False, f"Error updating professor: {e}"

    @staticmethod
    def delete_student(student_id):
        """
        Delete a student and all related records
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Enable foreign keys to ensure CASCADE works
            cursor.execute("PRAGMA foreign_keys = ON")

            # Get the user_id before deleting the student
            cursor.execute("SELECT user_id FROM students WHERE id = ?", (student_id,))
            user_data = cursor.fetchone()

            if not user_data:
                close_connection(conn)
                return False, "Student not found"

            user_id = user_data['user_id']

            # Delete the student record first
            # This will automatically cascade delete all related records due to ON DELETE CASCADE
            cursor.execute("DELETE FROM students WHERE id = ?", (student_id,))

            # Delete the user record
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

            conn.commit()
            close_connection(conn)

            return True, "Student deleted successfully"

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"SQLite error deleting student: {e}")
            return False, f"Database error: {e}"
        except Exception as e:
            if conn:
                conn.rollback()
                close_connection(conn)
            print(f"Error deleting student: {e}")
            return False, f"Error deleting student: {e}"

    @staticmethod
    def delete_professor(professor_id):
        """
        Delete a professor
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if professor is assigned to any courses
            cursor.execute(
                "SELECT COUNT(*) FROM professor_assignments WHERE professor_id = ?",
                (professor_id,)
            )
            assignments_count = cursor.fetchone()[0]

            if assignments_count > 0:
                close_connection(conn)
                return False, "Cannot delete professor because they are assigned to courses"

            # Delete professor
            cursor.execute("DELETE FROM professors WHERE id = ?", (professor_id,))

            conn.commit()
            close_connection(conn)

            return True, "Professor deleted successfully"

        except Exception as e:
            print(f"Error deleting professor: {e}")
            return False, f"Error deleting professor: {e}"

    @classmethod
    def show_departments(cls):
        """
        Show the departments list in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_departments()
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_edit_department_form(cls, department_id):
        """
        Show the edit department form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_edit_department(department_id)
        else:
            print("Admin dashboard reference not set")

    @classmethod
    def show_edit_semester_form(cls, semester_id):
        """
        Show the edit semester form in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_edit_semester(semester_id)
        else:
            print("Admin dashboard reference not set")

    @staticmethod
    def update_department(department_id, code, name):
        """
        Update department information
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if department code already exists for another department
            cursor.execute(
                "SELECT id FROM departments WHERE code = ? AND id != ?",
                (code, department_id)
            )
            existing_department = cursor.fetchone()

            if existing_department:
                close_connection(conn)
                return False, f"Department Code {code} is already in use"

            # Check if department name already exists for another department
            cursor.execute(
                "SELECT id FROM departments WHERE name = ? AND id != ?",
                (name, department_id)
            )
            existing_name = cursor.fetchone()

            if existing_name:
                close_connection(conn)
                return False, f"Department Name {name} is already in use"

            # Update department
            cursor.execute("""
                UPDATE departments
                SET code = ?, name = ?
                WHERE id = ?
            """, (code, name, department_id))

            conn.commit()
            close_connection(conn)

            return True, "Department updated successfully"

        except Exception as e:
            print(f"Error updating department: {e}")
            return False, f"Error updating department: {e}"

    @staticmethod
    def delete_department(department_id):
        """
        Delete a department
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if department has any courses
            cursor.execute(
                "SELECT COUNT(*) FROM courses WHERE department_id = ?",
                (department_id,)
            )
            courses_count = cursor.fetchone()[0]

            if courses_count > 0:
                close_connection(conn)
                return False, "Cannot delete department because it has courses assigned to it"

            # Check if department has any professors
            cursor.execute(
                "SELECT COUNT(*) FROM professors WHERE department_id = ?",
                (department_id,)
            )
            professors_count = cursor.fetchone()[0]

            if professors_count > 0:
                close_connection(conn)
                return False, "Cannot delete department because it has professors assigned to it"

            # Check if department has any students
            cursor.execute(
                "SELECT COUNT(*) FROM students WHERE department_id = ?",
                (department_id,)
            )
            students_count = cursor.fetchone()[0]

            if students_count > 0:
                close_connection(conn)
                return False, "Cannot delete department because it has students assigned to it"

            # Delete department
            cursor.execute("DELETE FROM departments WHERE id = ?", (department_id,))

            conn.commit()
            close_connection(conn)

            return True, "Department deleted successfully"

        except Exception as e:
            print(f"Error deleting department: {e}")
            return False, f"Error deleting department: {e}"

    @classmethod
    def show_professor_assignments(cls, professor_id):
        """
        Show the professor assignments view in the admin dashboard
        """
        if cls.admin_dashboard:
            cls.admin_dashboard.show_professor_assignments(professor_id)
        else:
            print("Admin dashboard reference not set")

    @staticmethod
    def remove_professor_assignment(assignment_id):
        """
        Remove a professor assignment
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Delete assignment
            cursor.execute("DELETE FROM professor_assignments WHERE id = ?", (assignment_id,))

            conn.commit()
            close_connection(conn)

            return True, "Assignment removed successfully"

        except Exception as e:
            print(f"Error removing assignment: {e}")
            return False, f"Error removing assignment: {e}"
